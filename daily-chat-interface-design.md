# Daily Chat Interface Design Specification

## Overview
The daily chat interface serves as the primary journaling experience where each day represents a new conversation session between the AI and user. The interface follows a therapy-guided approach with structured prompts and gentle interactions.

## Core Design Principles
- **Emotionally Safe**: Calming colors, gentle transitions, non-judgmental language
- **Distraction-Free**: Minimal UI, focus on content, progressive disclosure
- **Conversational**: Natural flow between AI prompts and user responses
- **Accessible**: Screen reader friendly, keyboard navigation, high contrast

## Chat Message Layout and Styling

### AI Message Component
```typescript
interface AIMessageProps {
  prompt: string;
  promptType: 'thoughts' | 'energy' | 'sleep' | 'labels' | 'breathing';
  timestamp: Date;
  isActive?: boolean;
}

// Visual Design:
// ┌─────────────────────────────────────────┐
// │ 🧠 AI Therapist                         │
// │ ┌─────────────────────────────────────┐ │
// │ │ "What thoughts energized you today? │ │
// │ │ Write the wonder, not just the      │ │
// │ │ weight..."                          │ │
// │ └─────────────────────────────────────┘ │
// │ 2:30 PM                                 │
// └─────────────────────────────────────────┘
```

**Styling Specifications:**
- **Background**: `var(--therapeutic-blue-50)` with subtle border
- **Typography**: `journal-prompt` class with gentle purple accent
- **Spacing**: 24px padding, 16px margin bottom
- **Border Radius**: 12px (slightly more rounded than default)
- **Icon**: Brain emoji or custom therapeutic icon
- **Animation**: Gentle fade-in with 0.5s duration

### User Response Component
```typescript
interface UserResponseProps {
  content: string;
  promptType: string;
  timestamp: Date;
  isEditing?: boolean;
  autoSave?: boolean;
}

// Visual Design:
// ┌─────────────────────────────────────────┐
// │                              You 🌱    │
// │ ┌─────────────────────────────────────┐ │
// │ │ "Today I felt energized when I      │ │
// │ │ completed my morning walk and saw   │ │
// │ │ the sunrise. It reminded me..."     │ │
// │ └─────────────────────────────────────┘ │
// │                                2:35 PM │
// └─────────────────────────────────────────┘
```

**Styling Specifications:**
- **Background**: `var(--mindful-green-50)` for positive responses
- **Typography**: `journal-entry` class with comfortable line height
- **Alignment**: Right-aligned for user messages
- **Auto-resize**: Textarea grows with content
- **Save Indicator**: Subtle "Saved" indicator with gentle-pulse animation

## Date/Session Organization System

### Daily Session Header
```typescript
interface DaySessionHeaderProps {
  date: Date;
  completionStatus: 'not-started' | 'in-progress' | 'completed';
  streakCount?: number;
}

// Visual Design:
// ┌─────────────────────────────────────────┐
// │ 🗓️ Today, January 15th                  │
// │ Day 7 of your reflection journey        │
// │ ●●●●●○○ (5/7 prompts completed)        │
// └─────────────────────────────────────────┘
```

**Features:**
- **Date Display**: Human-friendly format ("Today", "Yesterday", "3 days ago")
- **Progress Indicator**: Visual dots showing completion status
- **Streak Counter**: Motivational streak display
- **Gentle Encouragement**: Supportive messaging based on progress

### Session Navigation
```typescript
interface SessionNavigationProps {
  currentDate: Date;
  availableDates: Date[];
  onDateChange: (date: Date) => void;
}

// Visual Design:
// ┌─────────────────────────────────────────┐
// │ ← Jan 14    Today, Jan 15    Jan 16 →   │
// │ [●●●●●●●]   [●●●●●○○]      [○○○○○○○]   │
// └─────────────────────────────────────────┘
```

**Navigation Features:**
- **Swipe Gestures**: Left/right swipe on mobile
- **Keyboard Navigation**: Arrow keys for date navigation
- **Visual Indicators**: Completion status for each day
- **Smooth Transitions**: Slide animations between days

## Multi-Step Journal Form Components

### Journal Flow Container
```typescript
interface JournalFlowProps {
  currentStep: number;
  totalSteps: number;
  onStepComplete: (step: number, data: any) => void;
  autoSave: boolean;
}
```

### Step 1: Thoughts vs Energy
```typescript
interface ThoughtsEnergyStepProps {
  energizingThoughts: string;
  drainingThoughts: string;
  onUpdate: (data: Partial<ThoughtsEnergyData>) => void;
}

// Visual Design:
// ┌─────────────────────────────────────────┐
// │ 🧠 What thoughts energized you today?   │
// │ ┌─────────────────────────────────────┐ │
// │ │ Write the wonder, not just the      │ │
// │ │ weight...                           │ │
// │ └─────────────────────────────────────┘ │
// │                                         │
// │ 🔋 What thoughts drained your energy?   │
// │ ┌─────────────────────────────────────┐ │
// │ │ Sometimes the heaviest thoughts     │ │
// │ │ teach us the most...                │ │
// │ └─────────────────────────────────────┘ │
// │                                         │
// │ [Continue] →                            │
// └─────────────────────────────────────────┘
```

### Step 2: Existence vs Drain
```typescript
interface ExistenceDrainStepProps {
  feltAliveWith: string;
  feltDrainedBy: string;
  onUpdate: (data: Partial<ExistenceDrainData>) => void;
}
```

### Step 3: Sleep Cycle
```typescript
interface SleepCycleStepProps {
  hoursSlept: number;
  sleepTime: string;
  wakeTime: string;
  qualityRating: number; // 1-10
  dreamNotes: string;
  onUpdate: (data: Partial<SleepData>) => void;
}

// Visual Design includes:
// - Time picker components
// - Quality slider with emoji indicators
// - Optional dream notes textarea
```

### Step 4: Labels and Truth
```typescript
interface LabelsStepProps {
  labelAssigned: string;
  theirStory: string;
  yourTruth: string;
  onUpdate: (data: Partial<LabelsData>) => void;
}
```

### Step 5: Box Breathing Exercise
```typescript
interface BoxBreathingStepProps {
  stateBefore: string;
  stateAfter: string;
  completedCycles: number;
  onComplete: (data: BreathingData) => void;
}
```

## Box Breathing Exercise Interface

### Breathing Timer Component
```typescript
interface BreathingTimerProps {
  isActive: boolean;
  currentPhase: 'inhale' | 'hold1' | 'exhale' | 'hold2';
  cycleCount: number;
  onPhaseChange: (phase: string) => void;
  onComplete: () => void;
}

// Visual Design:
// ┌─────────────────────────────────────────┐
// │           🫁 Box Breathing              │
// │                                         │
// │        ┌─────────────────┐              │
// │        │                 │              │
// │        │    Inhale...    │              │
// │        │       4s        │              │
// │        │                 │              │
// │        └─────────────────┘              │
// │                                         │
// │ Cycle 3 of 4                           │
// │ [Pause] [Stop]                         │
// └─────────────────────────────────────────┘
```

**Animation Specifications:**
- **Breathing Circle**: Scales from 1.0 to 1.2 during inhale/exhale
- **Color Transitions**: Gentle blue to green gradient during breathing
- **Timer Display**: Large, clear countdown with smooth transitions
- **Phase Indicators**: Visual cues for each breathing phase

### Reflection Component
```typescript
interface BreathingReflectionProps {
  stateBefore: string;
  stateAfter: string;
  onUpdate: (field: 'before' | 'after', value: string) => void;
}

// Visual Design:
// ┌─────────────────────────────────────────┐
// │ 🌅 How did you feel before breathing?   │
// │ ┌─────────────────────────────────────┐ │
// │ │ Anxious, scattered thoughts...      │ │
// │ └─────────────────────────────────────┘ │
// │                                         │
// │ 🌸 How do you feel now?                │
// │ ┌─────────────────────────────────────┐ │
// │ │ More centered, calmer...            │ │
// │ └─────────────────────────────────────┘ │
// └─────────────────────────────────────────┘
```

## Input Validation and Auto-Save Patterns

### Gentle Validation
```typescript
interface GentleValidationProps {
  field: string;
  value: string;
  isRequired?: boolean;
  minLength?: number;
  customMessage?: string;
}
```

**Validation Approach:**
- **No harsh red errors**: Use gentle orange/amber for guidance
- **Encouraging messages**: "Take your time..." instead of "Required field"
- **Progressive validation**: Only validate after user stops typing
- **Contextual hints**: Helpful suggestions rather than strict rules

### Auto-Save Implementation
```typescript
interface AutoSaveProps {
  data: any;
  saveInterval: number; // milliseconds
  onSave: (data: any) => Promise<void>;
  showIndicator?: boolean;
}
```

**Auto-Save Features:**
- **Debounced saving**: 2-second delay after user stops typing
- **Visual feedback**: Subtle "Saving..." and "Saved" indicators
- **Offline support**: Queue saves when offline, sync when online
- **Error handling**: Gentle retry mechanism for failed saves

## Responsive Design Specifications

### Mobile Layout (320px - 768px)
- **Single column**: Full-width messages
- **Touch-friendly**: 44px minimum touch targets
- **Swipe navigation**: Between days and steps
- **Simplified header**: Condensed date/progress display

### Tablet Layout (768px - 1024px)
- **Wider messages**: Max-width with centered alignment
- **Side navigation**: Optional day picker sidebar
- **Enhanced breathing**: Larger breathing circle animation

### Desktop Layout (1024px+)
- **Optimal reading width**: 680px max-width for messages
- **Keyboard shortcuts**: Tab navigation, Enter to continue
- **Enhanced interactions**: Hover states, smoother animations

## Component Implementation Examples

### Chat Message Component (shadcn/ui based)
```typescript
import { Card, CardContent } from "@/components/ui/card"
import { cn } from "@/lib/utils"

interface ChatMessageProps {
  type: 'ai' | 'user';
  content: string;
  timestamp: Date;
  promptType?: string;
  className?: string;
}

export function ChatMessage({ type, content, timestamp, promptType, className }: ChatMessageProps) {
  return (
    <div className={cn(
      "flex w-full mb-4",
      type === 'user' ? "justify-end" : "justify-start",
      className
    )}>
      <Card className={cn(
        "max-w-[85%] md:max-w-[70%]",
        type === 'ai'
          ? "bg-therapeutic-blue-50 border-therapeutic-blue-100"
          : "bg-mindful-green-50 border-mindful-green-100"
      )}>
        <CardContent className="p-4">
          <div className="flex items-start gap-3">
            {type === 'ai' && (
              <div className="w-8 h-8 rounded-full bg-therapeutic-blue-500 flex items-center justify-center text-white text-sm">
                🧠
              </div>
            )}
            <div className="flex-1">
              <p className={cn(
                "text-sm leading-relaxed",
                type === 'ai' ? "journal-prompt" : "journal-entry"
              )}>
                {content}
              </p>
              <time className="text-xs text-muted-foreground mt-2 block">
                {timestamp.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })}
              </time>
            </div>
            {type === 'user' && (
              <div className="w-8 h-8 rounded-full bg-mindful-green-500 flex items-center justify-center text-white text-sm">
                🌱
              </div>
            )}
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
```

### Journal Input Component
```typescript
import { Textarea } from "@/components/ui/textarea"
import { Label } from "@/components/ui/label"
import { useState, useEffect } from "react"

interface JournalInputProps {
  label: string;
  placeholder: string;
  value: string;
  onChange: (value: string) => void;
  autoSave?: boolean;
  minHeight?: string;
}

export function JournalInput({
  label,
  placeholder,
  value,
  onChange,
  autoSave = true,
  minHeight = "120px"
}: JournalInputProps) {
  const [saveStatus, setSaveStatus] = useState<'idle' | 'saving' | 'saved'>('idle');

  useEffect(() => {
    if (!autoSave || !value) return;

    const timer = setTimeout(() => {
      setSaveStatus('saving');
      // Simulate save
      setTimeout(() => setSaveStatus('saved'), 500);
    }, 2000);

    return () => clearTimeout(timer);
  }, [value, autoSave]);

  return (
    <div className="space-y-2">
      <div className="flex items-center justify-between">
        <Label className="journal-prompt">{label}</Label>
        {autoSave && saveStatus !== 'idle' && (
          <span className={cn(
            "text-xs",
            saveStatus === 'saving' ? "text-muted-foreground" : "text-mindful-green-600"
          )}>
            {saveStatus === 'saving' ? 'Saving...' : 'Saved'}
          </span>
        )}
      </div>
      <Textarea
        placeholder={placeholder}
        value={value}
        onChange={(e) => onChange(e.target.value)}
        className={cn(
          "journal-entry resize-none gentle-transition",
          "focus:border-therapeutic-blue-500 focus:ring-therapeutic-blue-500/20"
        )}
        style={{ minHeight }}
      />
    </div>
  )
}
```
