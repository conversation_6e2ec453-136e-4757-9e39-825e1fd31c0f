# Visual Design System Integration

## Overview
This document extends the existing MindMirror design system to support mental health-focused UI components while maintaining consistency with the established TailwindCSS + shadcn/ui foundation.

## Extended Color Palette Integration

### Mental Health Color Variables (CSS Integration)
```css
/* Add to apps/web/src/index.css after existing color variables */

:root {
  /* Therapeutic Colors - extending existing OKLCH system */
  --therapeutic-blue-50: oklch(0.97 0.013 240);
  --therapeutic-blue-100: oklch(0.93 0.025 240);
  --therapeutic-blue-200: oklch(0.88 0.05 240);
  --therapeutic-blue-300: oklch(0.78 0.08 240);
  --therapeutic-blue-400: oklch(0.68 0.1 240);
  --therapeutic-blue-500: oklch(0.6 0.118 240);
  --therapeutic-blue-600: oklch(0.5 0.142 240);
  --therapeutic-blue-700: oklch(0.42 0.155 240);
  --therapeutic-blue-800: oklch(0.35 0.165 240);
  --therapeutic-blue-900: oklch(0.28 0.175 240);

  /* Mindful Greens - for positive energy states */
  --mindful-green-50: oklch(0.97 0.013 150);
  --mindful-green-100: oklch(0.93 0.025 150);
  --mindful-green-200: oklch(0.88 0.05 150);
  --mindful-green-300: oklch(0.78 0.08 150);
  --mindful-green-400: oklch(0.68 0.1 150);
  --mindful-green-500: oklch(0.6 0.118 150);
  --mindful-green-600: oklch(0.5 0.142 150);
  --mindful-green-700: oklch(0.42 0.155 150);
  --mindful-green-800: oklch(0.35 0.165 150);
  --mindful-green-900: oklch(0.28 0.175 150);

  /* Gentle Purples - for reflection and introspection */
  --gentle-purple-50: oklch(0.97 0.013 280);
  --gentle-purple-100: oklch(0.93 0.025 280);
  --gentle-purple-200: oklch(0.88 0.05 280);
  --gentle-purple-300: oklch(0.78 0.08 280);
  --gentle-purple-400: oklch(0.68 0.1 280);
  --gentle-purple-500: oklch(0.6 0.118 280);
  --gentle-purple-600: oklch(0.5 0.142 280);
  --gentle-purple-700: oklch(0.42 0.155 280);
  --gentle-purple-800: oklch(0.35 0.165 280);
  --gentle-purple-900: oklch(0.28 0.175 280);

  /* Warm Neutrals - for comfort and safety */
  --warm-neutral-50: oklch(0.97 0.005 60);
  --warm-neutral-100: oklch(0.93 0.01 60);
  --warm-neutral-200: oklch(0.88 0.015 60);
  --warm-neutral-300: oklch(0.78 0.02 60);
  --warm-neutral-400: oklch(0.68 0.025 60);
  --warm-neutral-500: oklch(0.6 0.03 60);
  --warm-neutral-600: oklch(0.5 0.035 60);
  --warm-neutral-700: oklch(0.42 0.04 60);
  --warm-neutral-800: oklch(0.35 0.045 60);
  --warm-neutral-900: oklch(0.28 0.05 60);

  /* Semantic Color Mappings */
  --energy-high: var(--mindful-green-500);
  --energy-medium: var(--warm-neutral-500);
  --energy-low: var(--therapeutic-blue-500);
  --energy-drain: oklch(0.6 0.08 20);

  --mood-positive: var(--mindful-green-500);
  --mood-neutral: var(--warm-neutral-500);
  --mood-reflective: var(--gentle-purple-500);
  --mood-challenging: oklch(0.65 0.1 40);

  /* Breathing Exercise Colors */
  --breathe-inhale: var(--therapeutic-blue-400);
  --breathe-hold: var(--gentle-purple-400);
  --breathe-exhale: var(--mindful-green-400);
  --breathe-rest: var(--warm-neutral-400);
}

.dark {
  /* Dark mode adjustments for mental health colors */
  --therapeutic-blue-50: oklch(0.15 0.02 240);
  --therapeutic-blue-100: oklch(0.2 0.03 240);
  --mindful-green-50: oklch(0.15 0.02 150);
  --mindful-green-100: oklch(0.2 0.03 150);
  --gentle-purple-50: oklch(0.15 0.02 280);
  --gentle-purple-100: oklch(0.2 0.03 280);
  --warm-neutral-50: oklch(0.15 0.005 60);
  --warm-neutral-100: oklch(0.2 0.01 60);
}
```

### TailwindCSS Configuration Extension
```javascript
// Add to apps/web/tailwind.config.js
module.exports = {
  theme: {
    extend: {
      colors: {
        'therapeutic-blue': {
          50: 'var(--therapeutic-blue-50)',
          100: 'var(--therapeutic-blue-100)',
          200: 'var(--therapeutic-blue-200)',
          300: 'var(--therapeutic-blue-300)',
          400: 'var(--therapeutic-blue-400)',
          500: 'var(--therapeutic-blue-500)',
          600: 'var(--therapeutic-blue-600)',
          700: 'var(--therapeutic-blue-700)',
          800: 'var(--therapeutic-blue-800)',
          900: 'var(--therapeutic-blue-900)',
        },
        'mindful-green': {
          50: 'var(--mindful-green-50)',
          100: 'var(--mindful-green-100)',
          200: 'var(--mindful-green-200)',
          300: 'var(--mindful-green-300)',
          400: 'var(--mindful-green-400)',
          500: 'var(--mindful-green-500)',
          600: 'var(--mindful-green-600)',
          700: 'var(--mindful-green-700)',
          800: 'var(--mindful-green-800)',
          900: 'var(--mindful-green-900)',
        },
        'gentle-purple': {
          50: 'var(--gentle-purple-50)',
          100: 'var(--gentle-purple-100)',
          200: 'var(--gentle-purple-200)',
          300: 'var(--gentle-purple-300)',
          400: 'var(--gentle-purple-400)',
          500: 'var(--gentle-purple-500)',
          600: 'var(--gentle-purple-600)',
          700: 'var(--gentle-purple-700)',
          800: 'var(--gentle-purple-800)',
          900: 'var(--gentle-purple-900)',
        },
        'warm-neutral': {
          50: 'var(--warm-neutral-50)',
          100: 'var(--warm-neutral-100)',
          200: 'var(--warm-neutral-200)',
          300: 'var(--warm-neutral-300)',
          400: 'var(--warm-neutral-400)',
          500: 'var(--warm-neutral-500)',
          600: 'var(--warm-neutral-600)',
          700: 'var(--warm-neutral-700)',
          800: 'var(--warm-neutral-800)',
          900: 'var(--warm-neutral-900)',
        },
        'energy': {
          high: 'var(--energy-high)',
          medium: 'var(--energy-medium)',
          low: 'var(--energy-low)',
          drain: 'var(--energy-drain)',
        },
        'mood': {
          positive: 'var(--mood-positive)',
          neutral: 'var(--mood-neutral)',
          reflective: 'var(--mood-reflective)',
          challenging: 'var(--mood-challenging)',
        }
      }
    }
  }
}
```

## Animation and Transition Specifications

### Breathing Exercise Animations
```css
/* Add to apps/web/src/index.css */

@keyframes breathe-in {
  0% { 
    transform: scale(1); 
    opacity: 0.7;
    background-color: var(--breathe-inhale);
  }
  100% { 
    transform: scale(1.15); 
    opacity: 1;
    background-color: var(--breathe-hold);
  }
}

@keyframes breathe-out {
  0% { 
    transform: scale(1.15); 
    opacity: 1;
    background-color: var(--breathe-hold);
  }
  100% { 
    transform: scale(1); 
    opacity: 0.7;
    background-color: var(--breathe-exhale);
  }
}

@keyframes gentle-pulse {
  0%, 100% { opacity: 0.8; }
  50% { opacity: 1; }
}

@keyframes fade-in-up {
  from { 
    opacity: 0; 
    transform: translateY(20px); 
  }
  to { 
    opacity: 1; 
    transform: translateY(0); 
  }
}

@keyframes slide-in-right {
  from { 
    opacity: 0; 
    transform: translateX(30px); 
  }
  to { 
    opacity: 1; 
    transform: translateX(0); 
  }
}

@keyframes chart-draw {
  from { 
    stroke-dashoffset: 1000; 
  }
  to { 
    stroke-dashoffset: 0; 
  }
}

/* Utility Classes */
.breathe-in {
  animation: breathe-in 4s ease-in-out;
}

.breathe-out {
  animation: breathe-out 4s ease-in-out;
}

.gentle-pulse {
  animation: gentle-pulse 2s ease-in-out infinite;
}

.fade-in-up {
  animation: fade-in-up 0.6s ease-out;
}

.slide-in-right {
  animation: slide-in-right 0.5s ease-out;
}

.gentle-transition {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.smooth-transition {
  transition: all 0.2s ease-in-out;
}

.slow-transition {
  transition: all 0.5s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}
```

### Chart Animation Utilities
```css
.chart-line {
  stroke-dasharray: 1000;
  stroke-dashoffset: 1000;
  animation: chart-draw 2s ease-in-out forwards;
}

.chart-bar {
  transform: scaleY(0);
  transform-origin: bottom;
  animation: bar-grow 1s ease-out forwards;
}

@keyframes bar-grow {
  to { transform: scaleY(1); }
}

.stagger-1 { animation-delay: 0.1s; }
.stagger-2 { animation-delay: 0.2s; }
.stagger-3 { animation-delay: 0.3s; }
.stagger-4 { animation-delay: 0.4s; }
.stagger-5 { animation-delay: 0.5s; }
```

## Typography Hierarchy for Journal Content

### Extended Typography Classes
```css
/* Add to apps/web/src/index.css */

.journal-heading {
  font-family: var(--font-sans);
  font-weight: 600;
  line-height: 1.3;
  letter-spacing: -0.02em;
  color: var(--foreground);
}

.journal-prompt {
  font-family: var(--font-sans);
  font-weight: 500;
  font-size: 0.95rem;
  line-height: 1.5;
  letter-spacing: 0.01em;
  color: var(--gentle-purple-600);
}

.journal-entry {
  font-family: var(--font-sans);
  font-weight: 400;
  font-size: 1rem;
  line-height: 1.7;
  letter-spacing: 0.01em;
  color: var(--foreground);
}

.journal-metadata {
  font-family: var(--font-sans);
  font-weight: 400;
  font-size: 0.875rem;
  line-height: 1.4;
  color: var(--muted-foreground);
}

.poetic-placeholder {
  font-family: var(--font-sans);
  font-weight: 400;
  font-style: italic;
  color: var(--muted-foreground);
  opacity: 0.8;
}

.insight-text {
  font-family: var(--font-sans);
  font-weight: 500;
  font-size: 0.9rem;
  line-height: 1.6;
  color: var(--therapeutic-blue-700);
}

.metric-value {
  font-family: var(--font-sans);
  font-weight: 700;
  font-size: 1.875rem;
  line-height: 1.2;
  letter-spacing: -0.025em;
  color: var(--foreground);
}

.metric-label {
  font-family: var(--font-sans);
  font-weight: 500;
  font-size: 0.75rem;
  line-height: 1.3;
  letter-spacing: 0.05em;
  text-transform: uppercase;
  color: var(--muted-foreground);
}

/* Responsive Typography */
@media (max-width: 768px) {
  .journal-entry {
    font-size: 0.95rem;
    line-height: 1.6;
  }

  .metric-value {
    font-size: 1.5rem;
  }

  .journal-prompt {
    font-size: 0.9rem;
  }
}
```

## Responsive Design Strategy

### Breakpoint System (extending TailwindCSS defaults)
```javascript
// TailwindCSS breakpoint extensions
module.exports = {
  theme: {
    screens: {
      'xs': '320px',    // Small phones
      'sm': '640px',    // Large phones
      'md': '768px',    // Tablets
      'lg': '1024px',   // Small laptops
      'xl': '1280px',   // Large laptops
      '2xl': '1536px',  // Desktops

      // Mental health app specific breakpoints
      'journal': '480px',    // Optimal journal reading width
      'dashboard': '1200px', // Dashboard optimal width
    }
  }
}
```

### Mobile-First Component Patterns
```css
/* Mobile-first responsive utilities */
.journal-container {
  @apply px-4 mx-auto;
  max-width: 100%;
}

@media (min-width: 480px) {
  .journal-container {
    @apply px-6;
    max-width: 480px;
  }
}

@media (min-width: 768px) {
  .journal-container {
    @apply px-8;
    max-width: 680px;
  }
}

@media (min-width: 1024px) {
  .journal-container {
    max-width: 720px;
  }
}

/* Dashboard responsive grid */
.dashboard-grid {
  @apply grid gap-4;
  grid-template-columns: 1fr;
}

@media (min-width: 768px) {
  .dashboard-grid {
    @apply gap-6;
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (min-width: 1200px) {
  .dashboard-grid {
    @apply gap-8;
    grid-template-columns: repeat(3, 1fr);
  }
}

/* Chat message responsive sizing */
.chat-message {
  @apply max-w-full;
}

@media (min-width: 640px) {
  .chat-message {
    @apply max-w-[85%];
  }
}

@media (min-width: 768px) {
  .chat-message {
    @apply max-w-[70%];
  }
}

@media (min-width: 1024px) {
  .chat-message {
    @apply max-w-[60%];
  }
}
```

## Accessibility Enhancements

### Focus Management
```css
/* Enhanced focus styles for mental health context */
.focus-therapeutic {
  @apply outline-none;
  box-shadow: 0 0 0 3px var(--therapeutic-blue-500);
  border-color: var(--therapeutic-blue-500);
}

.focus-gentle {
  @apply outline-none;
  box-shadow: 0 0 0 2px var(--gentle-purple-400);
  border-color: var(--gentle-purple-400);
}

/* High contrast mode support */
@media (prefers-contrast: high) {
  .journal-prompt {
    color: var(--foreground);
    font-weight: 600;
  }

  .therapeutic-blue-50 {
    background-color: var(--background);
    border: 2px solid var(--therapeutic-blue-500);
  }

  .mindful-green-50 {
    background-color: var(--background);
    border: 2px solid var(--mindful-green-500);
  }
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
  .breathe-in,
  .breathe-out,
  .gentle-pulse,
  .fade-in-up,
  .slide-in-right {
    animation: none;
  }

  .gentle-transition,
  .smooth-transition,
  .slow-transition {
    transition: none;
  }
}
```

### Screen Reader Enhancements
```css
/* Screen reader only content */
.sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border: 0;
}

.sr-only-focusable:focus {
  position: static;
  width: auto;
  height: auto;
  padding: inherit;
  margin: inherit;
  overflow: visible;
  clip: auto;
  white-space: normal;
}

/* Chart accessibility */
.chart-accessible {
  position: relative;
}

.chart-accessible::after {
  content: attr(data-description);
  @apply sr-only;
}
```

## Component Variant Extensions

### Mental Health Button Variants
```typescript
// Extend existing button variants in components/ui/button.tsx
const buttonVariants = cva(
  // ... existing base classes
  {
    variants: {
      variant: {
        // ... existing variants
        therapeutic: "bg-therapeutic-blue-500 text-white hover:bg-therapeutic-blue-600 focus-visible:ring-therapeutic-blue-500/20",
        mindful: "bg-mindful-green-500 text-white hover:bg-mindful-green-600 focus-visible:ring-mindful-green-500/20",
        gentle: "bg-gentle-purple-500 text-white hover:bg-gentle-purple-600 focus-visible:ring-gentle-purple-500/20",
        breathing: "bg-gradient-to-r from-therapeutic-blue-400 to-mindful-green-400 text-white hover:from-therapeutic-blue-500 hover:to-mindful-green-500",
      },
      // ... existing size variants
    }
  }
)
```

### Card Variants for Mental Health Context
```typescript
// New card variants for mental health components
const cardVariants = cva(
  "bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm",
  {
    variants: {
      context: {
        default: "",
        therapeutic: "bg-therapeutic-blue-50 border-therapeutic-blue-100",
        mindful: "bg-mindful-green-50 border-mindful-green-100",
        reflective: "bg-gentle-purple-50 border-gentle-purple-100",
        warm: "bg-warm-neutral-50 border-warm-neutral-100",
      }
    },
    defaultVariants: {
      context: "default"
    }
  }
)
```

## Implementation Guidelines

### CSS Custom Properties Usage
```css
/* Use semantic color names in components */
.energy-indicator {
  background-color: var(--energy-medium);
  border-color: var(--energy-medium);
}

.energy-indicator[data-level="high"] {
  background-color: var(--energy-high);
  border-color: var(--energy-high);
}

.energy-indicator[data-level="low"] {
  background-color: var(--energy-low);
  border-color: var(--energy-low);
}

.energy-indicator[data-level="drain"] {
  background-color: var(--energy-drain);
  border-color: var(--energy-drain);
}
```

### Component Composition Patterns
```typescript
// Example of composing mental health components
interface MentalHealthCardProps {
  context: 'therapeutic' | 'mindful' | 'reflective' | 'warm';
  children: React.ReactNode;
  className?: string;
}

export function MentalHealthCard({ context, children, className }: MentalHealthCardProps) {
  return (
    <Card className={cn(cardVariants({ context }), className)}>
      {children}
    </Card>
  )
}
```
```
