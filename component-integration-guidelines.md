# Component Integration Guidelines

## Overview
This document provides comprehensive guidelines for implementing the MindMirror UI components within the existing TanStack Start + Convex + shadcn/ui architecture.

## Implementation Guidelines for New Components

### Component Structure Standards
```typescript
// Standard component template for MindMirror
import * as React from "react"
import { cva, type VariantProps } from "class-variance-authority"
import { cn } from "@/lib/utils"

// Define component variants using CVA
const componentVariants = cva(
  "base-classes", // Base styling that applies to all variants
  {
    variants: {
      variant: {
        default: "default-styles",
        therapeutic: "therapeutic-blue-50 border-therapeutic-blue-100",
        mindful: "mindful-green-50 border-mindful-green-100",
        reflective: "gentle-purple-50 border-gentle-purple-100",
      },
      size: {
        sm: "small-size-styles",
        md: "medium-size-styles",
        lg: "large-size-styles",
      }
    },
    defaultVariants: {
      variant: "default",
      size: "md",
    }
  }
)

// Component interface with proper TypeScript typing
interface ComponentProps extends React.HTMLAttributes<HTMLDivElement>, 
  VariantProps<typeof componentVariants> {
  // Mental health specific props
  emotionalState?: 'positive' | 'neutral' | 'reflective' | 'challenging';
  energyLevel?: 'high' | 'medium' | 'low' | 'drain';
  
  // Accessibility props
  'aria-label'?: string;
  'aria-describedby'?: string;
  
  // Interaction props
  onSave?: () => void;
  autoSave?: boolean;
  gentleValidation?: boolean;
}

// Component implementation
const Component = React.forwardRef<HTMLDivElement, ComponentProps>(
  ({ className, variant, size, emotionalState, ...props }, ref) => {
    return (
      <div
        ref={ref}
        data-slot="component-name"
        className={cn(componentVariants({ variant, size, className }))}
        {...props}
      />
    )
  }
)

Component.displayName = "Component"

export { Component, componentVariants }
```

### File Organization
```
apps/web/src/components/
├── ui/                          # shadcn/ui components
│   ├── button.tsx
│   ├── card.tsx
│   └── ...
├── journal/                     # Journal-specific components
│   ├── chat-message.tsx
│   ├── journal-input.tsx
│   ├── breathing-timer.tsx
│   └── session-header.tsx
├── dashboard/                   # Dashboard components
│   ├── energy-chart.tsx
│   ├── mood-heatmap.tsx
│   ├── insights-panel.tsx
│   └── export-options.tsx
├── shared/                      # Shared mental health components
│   ├── mental-health-card.tsx
│   ├── energy-indicator.tsx
│   └── mood-badge.tsx
└── layout/                      # Layout components
    ├── journal-layout.tsx
    └── dashboard-layout.tsx
```

## State Management Patterns for Journal Data

### Convex Integration with TanStack Query
```typescript
// apps/web/src/hooks/useJournalData.ts
import { useSuspenseQuery, useMutation, useQueryClient } from "@tanstack/react-query"
import { convexQuery, convexMutation } from "@convex-dev/react-query"
import { api } from "@mindmirror/backend/convex/_generated/api"

export interface JournalEntry {
  _id: string;
  date: string;
  energizingThoughts: string;
  drainingThoughts: string;
  feltAliveWith: string;
  feltDrainedBy: string;
  sleepData: {
    hoursSlept: number;
    sleepTime: string;
    wakeTime: string;
    qualityRating: number;
    dreamNotes?: string;
  };
  labelsData: {
    labelAssigned: string;
    theirStory: string;
    yourTruth: string;
  };
  breathingData: {
    stateBefore: string;
    stateAfter: string;
    completedCycles: number;
  };
  completionStatus: 'not-started' | 'in-progress' | 'completed';
  createdAt: number;
  updatedAt: number;
}

// Hook for fetching journal entries
export function useJournalEntries(dateRange?: [Date, Date]) {
  return useSuspenseQuery(
    convexQuery(api.journal.getEntries, { 
      startDate: dateRange?.[0]?.getTime(),
      endDate: dateRange?.[1]?.getTime()
    })
  )
}

// Hook for creating/updating journal entries
export function useJournalMutations() {
  const queryClient = useQueryClient()
  
  const createEntry = useMutation({
    mutationFn: convexMutation(api.journal.create),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['journal'] })
    }
  })
  
  const updateEntry = useMutation({
    mutationFn: convexMutation(api.journal.update),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['journal'] })
    }
  })
  
  const deleteEntry = useMutation({
    mutationFn: convexMutation(api.journal.delete),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['journal'] })
    }
  })
  
  return { createEntry, updateEntry, deleteEntry }
}
```

### Auto-Save Implementation
```typescript
// apps/web/src/hooks/useAutoSave.ts
import { useEffect, useRef, useCallback } from 'react'
import { useMutation } from '@tanstack/react-query'
import { convexMutation } from '@convex-dev/react-query'
import { api } from '@mindmirror/backend/convex/_generated/api'

interface UseAutoSaveOptions {
  entryId?: string;
  data: Partial<JournalEntry>;
  delay?: number; // milliseconds
  enabled?: boolean;
}

export function useAutoSave({ 
  entryId, 
  data, 
  delay = 2000, 
  enabled = true 
}: UseAutoSaveOptions) {
  const timeoutRef = useRef<NodeJS.Timeout>()
  const [saveStatus, setSaveStatus] = useState<'idle' | 'saving' | 'saved' | 'error'>('idle')
  
  const saveMutation = useMutation({
    mutationFn: convexMutation(api.journal.autoSave),
    onMutate: () => setSaveStatus('saving'),
    onSuccess: () => setSaveStatus('saved'),
    onError: () => setSaveStatus('error'),
  })
  
  const debouncedSave = useCallback(() => {
    if (!enabled || !data) return
    
    if (timeoutRef.current) {
      clearTimeout(timeoutRef.current)
    }
    
    timeoutRef.current = setTimeout(() => {
      saveMutation.mutate({ entryId, data })
    }, delay)
  }, [entryId, data, delay, enabled, saveMutation])
  
  useEffect(() => {
    debouncedSave()
    
    return () => {
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current)
      }
    }
  }, [debouncedSave])
  
  useEffect(() => {
    if (saveStatus === 'saved') {
      const timer = setTimeout(() => setSaveStatus('idle'), 2000)
      return () => clearTimeout(timer)
    }
  }, [saveStatus])
  
  return { saveStatus, forceSave: () => saveMutation.mutate({ entryId, data }) }
}
```

### Form State Management
```typescript
// apps/web/src/hooks/useJournalForm.ts
import { useForm } from '@tanstack/react-form'
import { zodValidator } from '@tanstack/zod-form-adapter'
import { z } from 'zod'

const journalEntrySchema = z.object({
  energizingThoughts: z.string().min(1, "Please share what energized you"),
  drainingThoughts: z.string().min(1, "Please share what drained you"),
  feltAliveWith: z.string().min(1, "Please share what made you feel alive"),
  feltDrainedBy: z.string().min(1, "Please share what drained you"),
  sleepData: z.object({
    hoursSlept: z.number().min(0).max(24),
    sleepTime: z.string(),
    wakeTime: z.string(),
    qualityRating: z.number().min(1).max(10),
    dreamNotes: z.string().optional(),
  }),
  labelsData: z.object({
    labelAssigned: z.string(),
    theirStory: z.string(),
    yourTruth: z.string(),
  }),
  breathingData: z.object({
    stateBefore: z.string(),
    stateAfter: z.string(),
    completedCycles: z.number().min(0),
  }),
})

export function useJournalForm(initialData?: Partial<JournalEntry>) {
  const { updateEntry } = useJournalMutations()
  const { saveStatus } = useAutoSave({ 
    entryId: initialData?._id, 
    data: form.state.values 
  })
  
  const form = useForm({
    defaultValues: {
      energizingThoughts: initialData?.energizingThoughts || '',
      drainingThoughts: initialData?.drainingThoughts || '',
      feltAliveWith: initialData?.feltAliveWith || '',
      feltDrainedBy: initialData?.feltDrainedBy || '',
      sleepData: initialData?.sleepData || {
        hoursSlept: 8,
        sleepTime: '22:00',
        wakeTime: '06:00',
        qualityRating: 5,
        dreamNotes: '',
      },
      labelsData: initialData?.labelsData || {
        labelAssigned: '',
        theirStory: '',
        yourTruth: '',
      },
      breathingData: initialData?.breathingData || {
        stateBefore: '',
        stateAfter: '',
        completedCycles: 0,
      },
    },
    validatorAdapter: zodValidator,
    validators: {
      onChange: journalEntrySchema,
    },
    onSubmit: async ({ value }) => {
      await updateEntry.mutateAsync({
        id: initialData?._id,
        ...value,
      })
    },
  })
  
  return { form, saveStatus, isSubmitting: updateEntry.isPending }
}
```

## API Integration Points for Convex Backend

### Convex Schema Definition
```typescript
// packages/backend/convex/schema.ts
import { defineSchema, defineTable } from "convex/server"
import { v } from "convex/values"

export default defineSchema({
  journalEntries: defineTable({
    userId: v.optional(v.string()), // For future auth implementation
    date: v.string(), // ISO date string
    energizingThoughts: v.string(),
    drainingThoughts: v.string(),
    feltAliveWith: v.string(),
    feltDrainedBy: v.string(),
    sleepData: v.object({
      hoursSlept: v.number(),
      sleepTime: v.string(),
      wakeTime: v.string(),
      qualityRating: v.number(),
      dreamNotes: v.optional(v.string()),
    }),
    labelsData: v.object({
      labelAssigned: v.string(),
      theirStory: v.string(),
      yourTruth: v.string(),
    }),
    breathingData: v.object({
      stateBefore: v.string(),
      stateAfter: v.string(),
      completedCycles: v.number(),
    }),
    completionStatus: v.union(
      v.literal("not-started"),
      v.literal("in-progress"), 
      v.literal("completed")
    ),
    createdAt: v.number(),
    updatedAt: v.number(),
  })
    .index("by_date", ["date"])
    .index("by_creation_time", ["createdAt"])
    .index("by_completion_status", ["completionStatus"]),
    
  insights: defineTable({
    userId: v.optional(v.string()),
    type: v.union(
      v.literal("pattern"),
      v.literal("correlation"),
      v.literal("achievement"),
      v.literal("suggestion")
    ),
    title: v.string(),
    description: v.string(),
    confidence: v.number(), // 0-1
    actionable: v.optional(v.string()),
    relatedEntryIds: v.array(v.id("journalEntries")),
    createdAt: v.number(),
    dismissed: v.boolean(),
  })
    .index("by_type", ["type"])
    .index("by_creation_time", ["createdAt"])
    .index("by_dismissed", ["dismissed"]),
})
```

### Convex Functions Implementation
```typescript
// packages/backend/convex/journal.ts
import { mutation, query } from "./_generated/server"
import { v } from "convex/values"

export const create = mutation({
  args: {
    date: v.string(),
    energizingThoughts: v.string(),
    drainingThoughts: v.string(),
    // ... other fields
  },
  handler: async (ctx, args) => {
    const now = Date.now()

    return await ctx.db.insert("journalEntries", {
      ...args,
      completionStatus: "in-progress",
      createdAt: now,
      updatedAt: now,
    })
  },
})

export const update = mutation({
  args: {
    id: v.id("journalEntries"),
    energizingThoughts: v.optional(v.string()),
    drainingThoughts: v.optional(v.string()),
    // ... other optional fields
  },
  handler: async (ctx, { id, ...updates }) => {
    return await ctx.db.patch(id, {
      ...updates,
      updatedAt: Date.now(),
    })
  },
})

export const getEntries = query({
  args: {
    startDate: v.optional(v.number()),
    endDate: v.optional(v.number()),
    limit: v.optional(v.number()),
  },
  handler: async (ctx, { startDate, endDate, limit = 50 }) => {
    let query = ctx.db.query("journalEntries")

    if (startDate && endDate) {
      query = query.filter((q) =>
        q.and(
          q.gte(q.field("createdAt"), startDate),
          q.lte(q.field("createdAt"), endDate)
        )
      )
    }

    return await query
      .order("desc")
      .take(limit)
  },
})

export const getAnalytics = query({
  args: {
    timeRange: v.union(v.literal("week"), v.literal("month"), v.literal("quarter")),
  },
  handler: async (ctx, { timeRange }) => {
    const now = Date.now()
    const ranges = {
      week: 7 * 24 * 60 * 60 * 1000,
      month: 30 * 24 * 60 * 60 * 1000,
      quarter: 90 * 24 * 60 * 60 * 1000,
    }

    const startDate = now - ranges[timeRange]

    const entries = await ctx.db
      .query("journalEntries")
      .filter((q) => q.gte(q.field("createdAt"), startDate))
      .collect()

    // Calculate analytics
    const totalEntries = entries.length
    const completedEntries = entries.filter(e => e.completionStatus === "completed").length
    const avgSleepQuality = entries.reduce((sum, e) => sum + e.sleepData.qualityRating, 0) / totalEntries

    return {
      totalEntries,
      completedEntries,
      completionRate: completedEntries / totalEntries,
      avgSleepQuality,
      // Add more analytics as needed
    }
  },
})
```

## Testing Strategies for Mental Health UI

### Component Testing with Vitest and Testing Library
```typescript
// apps/web/src/components/journal/__tests__/chat-message.test.tsx
import { render, screen } from '@testing-library/react'
import { describe, it, expect } from 'vitest'
import { ChatMessage } from '../chat-message'

describe('ChatMessage', () => {
  it('renders AI message with therapeutic styling', () => {
    render(
      <ChatMessage
        type="ai"
        content="What thoughts energized you today?"
        timestamp={new Date('2024-01-15T14:30:00')}
        promptType="thoughts"
      />
    )

    expect(screen.getByText('What thoughts energized you today?')).toBeInTheDocument()
    expect(screen.getByText('2:30 PM')).toBeInTheDocument()
    expect(screen.getByRole('article')).toHaveClass('bg-therapeutic-blue-50')
  })

  it('renders user message with mindful styling', () => {
    render(
      <ChatMessage
        type="user"
        content="I felt energized by my morning walk"
        timestamp={new Date('2024-01-15T14:35:00')}
      />
    )

    expect(screen.getByText('I felt energized by my morning walk')).toBeInTheDocument()
    expect(screen.getByRole('article')).toHaveClass('bg-mindful-green-50')
  })

  it('has proper accessibility attributes', () => {
    render(
      <ChatMessage
        type="ai"
        content="How are you feeling?"
        timestamp={new Date()}
      />
    )

    const message = screen.getByRole('article')
    expect(message).toHaveAttribute('aria-label')
    expect(message).toHaveAttribute('data-testid', 'chat-message')
  })
})
```

### Integration Testing with Convex
```typescript
// apps/web/src/__tests__/journal-flow.test.tsx
import { render, screen, fireEvent, waitFor } from '@testing-library/react'
import { QueryClient, QueryClientProvider } from '@tanstack/react-query'
import { ConvexProvider } from 'convex/react'
import { describe, it, expect, beforeEach } from 'vitest'
import { JournalFlow } from '../components/journal/journal-flow'

// Mock Convex client
const mockConvexClient = {
  query: vi.fn(),
  mutation: vi.fn(),
  // ... other methods
}

describe('Journal Flow Integration', () => {
  let queryClient: QueryClient

  beforeEach(() => {
    queryClient = new QueryClient({
      defaultOptions: {
        queries: { retry: false },
        mutations: { retry: false },
      },
    })
  })

  const renderWithProviders = (component: React.ReactElement) => {
    return render(
      <ConvexProvider client={mockConvexClient}>
        <QueryClientProvider client={queryClient}>
          {component}
        </QueryClientProvider>
      </ConvexProvider>
    )
  }

  it('completes full journal entry flow', async () => {
    mockConvexClient.mutation.mockResolvedValue({ _id: 'test-entry-id' })

    renderWithProviders(<JournalFlow date={new Date('2024-01-15')} />)

    // Step 1: Thoughts vs Energy
    fireEvent.change(screen.getByLabelText(/energizing thoughts/i), {
      target: { value: 'Morning meditation was peaceful' }
    })
    fireEvent.change(screen.getByLabelText(/draining thoughts/i), {
      target: { value: 'Work stress about deadlines' }
    })
    fireEvent.click(screen.getByRole('button', { name: /continue/i }))

    // Step 2: Existence vs Drain
    await waitFor(() => {
      expect(screen.getByLabelText(/felt alive with/i)).toBeInTheDocument()
    })

    // Continue through all steps...

    // Verify auto-save functionality
    await waitFor(() => {
      expect(mockConvexClient.mutation).toHaveBeenCalledWith(
        expect.objectContaining({
          energizingThoughts: 'Morning meditation was peaceful',
          drainingThoughts: 'Work stress about deadlines',
        })
      )
    })
  })
})
```

### Accessibility Testing
```typescript
// apps/web/src/__tests__/accessibility.test.tsx
import { render } from '@testing-library/react'
import { axe, toHaveNoViolations } from 'jest-axe'
import { describe, it, expect } from 'vitest'
import { DashboardLayout } from '../components/layout/dashboard-layout'

expect.extend(toHaveNoViolations)

describe('Accessibility Tests', () => {
  it('dashboard layout has no accessibility violations', async () => {
    const { container } = render(
      <DashboardLayout>
        <div>Test content</div>
      </DashboardLayout>
    )

    const results = await axe(container)
    expect(results).toHaveNoViolations()
  })

  it('supports keyboard navigation', () => {
    render(<JournalInput label="Test input" placeholder="Type here..." />)

    const input = screen.getByRole('textbox')
    input.focus()

    expect(input).toHaveFocus()
    expect(input).toHaveAttribute('aria-label')
  })

  it('provides proper color contrast', () => {
    render(<Button variant="therapeutic">Save Entry</Button>)

    const button = screen.getByRole('button')
    const styles = getComputedStyle(button)

    // Test color contrast ratios
    expect(getContrastRatio(styles.color, styles.backgroundColor)).toBeGreaterThan(4.5)
  })
})
```

### Performance Testing
```typescript
// apps/web/src/__tests__/performance.test.tsx
import { render, screen } from '@testing-library/react'
import { describe, it, expect } from 'vitest'
import { EnergyChart } from '../components/dashboard/energy-chart'

describe('Performance Tests', () => {
  it('renders large datasets efficiently', () => {
    const largeDataset = Array.from({ length: 1000 }, (_, i) => ({
      date: new Date(Date.now() - i * 24 * 60 * 60 * 1000),
      energyLevel: Math.floor(Math.random() * 10) + 1,
      mood: 'positive' as const,
    }))

    const startTime = performance.now()
    render(<EnergyChart data={largeDataset} />)
    const endTime = performance.now()

    expect(endTime - startTime).toBeLessThan(100) // Should render in under 100ms
    expect(screen.getByRole('img', { name: /energy chart/i })).toBeInTheDocument()
  })

  it('implements virtualization for long lists', () => {
    const manyEntries = Array.from({ length: 500 }, (_, i) => ({
      id: `entry-${i}`,
      date: new Date(),
      content: `Entry ${i}`,
    }))

    render(<JournalHistory entries={manyEntries} />)

    // Should only render visible items
    const renderedItems = screen.getAllByTestId(/journal-entry-/)
    expect(renderedItems.length).toBeLessThan(50) // Only visible items rendered
  })
})
```
