# MindMirror UI Design Guide - Complete Implementation Specification

## Executive Summary

This comprehensive guide provides detailed UI design specifications for the MindMirror mental health journaling application. The designs are built upon the existing TailwindCSS + shadcn/ui foundation and specifically tailored for therapy-guided journaling and mental health data visualization.

## Project Context

**Technology Stack:**
- Frontend: React 19 + TanStack Start (SSR)
- Styling: TailwindCSS v4.1.3 + shadcn/ui "new-york" style
- Backend: Convex (Backend-as-a-Service)
- State Management: TanStack Query + Convex reactive data
- Build Tool: Vite + Bun package manager

**Design Philosophy:**
- Emotionally safe with calming color palettes
- Distraction-free with zero clutter approach
- Privacy-first with encrypted storage considerations
- Poetic UI with thoughtful placeholder text

## 1. Daily Chat Interface Design

### Core Concept
The daily chat interface transforms journaling into a conversational experience where each day represents a new therapy session. The AI guides users through structured prompts while maintaining a supportive, non-judgmental tone.

### Key Components

#### Chat Message Layout
```typescript
// AI Message Component
<Card className="bg-therapeutic-blue-50 border-therapeutic-blue-100 max-w-[85%]">
  <CardContent className="p-4">
    <div className="flex items-start gap-3">
      <div className="w-8 h-8 rounded-full bg-therapeutic-blue-500 flex items-center justify-center text-white">
        🧠
      </div>
      <div className="flex-1">
        <p className="journal-prompt text-sm leading-relaxed">
          "What thoughts energized you today? Write the wonder, not just the weight..."
        </p>
        <time className="text-xs text-muted-foreground mt-2 block">2:30 PM</time>
      </div>
    </div>
  </CardContent>
</Card>

// User Response Component  
<Card className="bg-mindful-green-50 border-mindful-green-100 max-w-[85%] ml-auto">
  <CardContent className="p-4">
    <div className="flex items-start gap-3">
      <div className="flex-1">
        <p className="journal-entry text-sm leading-relaxed">
          "Today I felt energized when I completed my morning walk..."
        </p>
        <time className="text-xs text-muted-foreground mt-2 block">2:35 PM</time>
      </div>
      <div className="w-8 h-8 rounded-full bg-mindful-green-500 flex items-center justify-center text-white">
        🌱
      </div>
    </div>
  </CardContent>
</Card>
```

#### Session Organization
- **Daily Headers**: Display date in human-friendly format ("Today", "Yesterday", "3 days ago")
- **Progress Indicators**: Visual dots showing completion status (●●●●●○○)
- **Streak Counter**: Motivational display of consecutive journaling days
- **Navigation**: Swipe gestures on mobile, arrow keys on desktop

#### Multi-Step Journal Form
1. **Thoughts vs Energy**: Energizing/draining thoughts with poetic placeholders
2. **Existence vs Drain**: What made you feel alive vs drained
3. **Sleep Cycle**: Hours, quality rating (1-10), dream notes
4. **Labels and Truth**: Others' labels vs your truth
5. **Box Breathing**: Guided 4-4-4-4 breathing with before/after reflection

#### Box Breathing Interface
```css
/* Breathing animation */
@keyframes breathe-in {
  0% { transform: scale(1); opacity: 0.7; background-color: var(--breathe-inhale); }
  100% { transform: scale(1.15); opacity: 1; background-color: var(--breathe-hold); }
}

.breathing-circle {
  width: 200px;
  height: 200px;
  border-radius: 50%;
  background: linear-gradient(45deg, var(--therapeutic-blue-400), var(--mindful-green-400));
  animation: breathe-in 4s ease-in-out infinite alternate;
}
```

### Input Patterns
- **Auto-save**: 2-second debounced saving with visual feedback
- **Gentle Validation**: Encouraging messages instead of harsh errors
- **Progressive Disclosure**: One step at a time to reduce overwhelm
- **Accessibility**: Screen reader support, keyboard navigation, high contrast

## 2. Statistics Dashboard Design

### Core Concept
The dashboard transforms raw journal data into meaningful insights through carefully designed visualizations that emphasize patterns and growth rather than judgment.

### Key Components

#### Overview Cards
```typescript
<div className="dashboard-grid grid gap-6 md:grid-cols-2 lg:grid-cols-3">
  <DashboardCard
    title="Total Entries"
    value="47"
    trend="up"
    trendValue="+3 this week"
    icon={<BarChart className="w-4 h-4" />}
  />
  <DashboardCard
    title="Avg Energy"
    value="7.2"
    trend="up"
    trendValue="+0.8 vs last week"
    icon={<Zap className="w-4 h-4" />}
  />
  <DashboardCard
    title="Current Streak"
    value="12 days"
    insight="Personal best!"
    icon={<Flame className="w-4 h-4" />}
  />
</div>
```

#### Data Visualizations

**Energy Trend Chart:**
- Line chart with gradient from energy-low to energy-high colors
- Interactive points showing specific thoughts/activities
- Insight panels highlighting patterns ("Energy peaks on Wednesdays")

**Sleep Quality Visualization:**
- Dual-axis chart showing hours slept vs quality rating
- Correlation indicators linking sleep to next-day energy
- Color-coded bars using therapeutic color palette

**Mood Pattern Heatmap:**
- Calendar-style grid with emoji/color coding
- Weekly/monthly view options
- Pattern recognition ("Fridays tend to be challenging")

#### Historical Navigation
- **Time Range Selector**: Week/Month/Quarter/Year buttons
- **Calendar Navigation**: Visual completion status for each day
- **Custom Date Ranges**: For detailed analysis periods

#### Search and Filter Interface
```typescript
<Card className="p-6">
  <div className="space-y-4">
    <Input 
      placeholder="Search thoughts, feelings, activities..."
      className="w-full"
    />
    <div className="flex flex-wrap gap-2">
      <Badge variant="outline">📅 Last 30 days</Badge>
      <Badge variant="outline">😊 All Moods</Badge>
      <Badge variant="outline">⚡ Energy: 1-10</Badge>
    </div>
    <div className="flex gap-2">
      <Button variant="outline">Clear Filters</Button>
      <Button variant="outline">Save Search</Button>
      <Button>Search</Button>
    </div>
  </div>
</Card>
```

### Export Functionality
- **PDF Reports**: Charts + insights for therapist sharing
- **CSV Data**: Spreadsheet-compatible for personal analysis
- **JSON Export**: Raw data for developers/researchers
- **Privacy Options**: Anonymization toggles for sensitive data

## 3. Visual Design Requirements

### Extended Color Palette
```css
:root {
  /* Therapeutic Blues - breathing exercises, calm states */
  --therapeutic-blue-50: oklch(0.97 0.013 240);
  --therapeutic-blue-500: oklch(0.6 0.118 240);
  
  /* Mindful Greens - positive energy, growth */
  --mindful-green-50: oklch(0.97 0.013 150);
  --mindful-green-500: oklch(0.6 0.118 150);
  
  /* Gentle Purples - reflection, introspection */
  --gentle-purple-50: oklch(0.97 0.013 280);
  --gentle-purple-500: oklch(0.6 0.118 280);
  
  /* Semantic mappings */
  --energy-high: var(--mindful-green-500);
  --energy-low: var(--therapeutic-blue-500);
  --mood-positive: var(--mindful-green-500);
  --mood-reflective: var(--gentle-purple-500);
}
```

### Typography Hierarchy
```css
.journal-prompt {
  font-weight: 500;
  font-size: 0.95rem;
  color: var(--gentle-purple-600);
  line-height: 1.5;
}

.journal-entry {
  font-size: 1rem;
  line-height: 1.7;
  letter-spacing: 0.01em;
}

.poetic-placeholder {
  font-style: italic;
  opacity: 0.8;
  color: var(--muted-foreground);
}
```

### Animation Specifications
- **Gentle Transitions**: 0.3s cubic-bezier(0.4, 0, 0.2, 1)
- **Breathing Animations**: 4s ease-in-out with scale and color changes
- **Chart Animations**: Staggered entrance with 2s duration
- **Reduced Motion**: Respects user preferences for accessibility

### Responsive Design
- **Mobile First**: 320px-768px optimized for journaling
- **Touch Friendly**: 44px minimum touch targets
- **Tablet Enhanced**: 768px-1024px with improved navigation
- **Desktop Optimal**: 1024px+ with full feature set

## 4. Accessibility Standards

### Mental Health Specific Requirements
- **High Contrast**: 4.5:1 minimum ratio for all text
- **Color Independence**: Never rely solely on color for information
- **Cognitive Load**: Simple language, consistent patterns
- **Error Handling**: Gentle, encouraging validation messages
- **Focus Management**: Logical tab order, visible focus indicators

### Screen Reader Support
- **Semantic HTML**: Proper heading hierarchy
- **ARIA Labels**: Descriptive labels for interactive elements
- **Live Regions**: Dynamic content announcements
- **Alternative Text**: Comprehensive descriptions for charts/images

## 5. Implementation Roadmap

### Phase 1: Foundation (Week 1-2)
- Extend color palette in TailwindCSS configuration
- Create base mental health component variants
- Implement typography classes and animation utilities

### Phase 2: Chat Interface (Week 3-4)
- Build chat message components
- Implement multi-step journal form
- Create breathing exercise interface
- Add auto-save functionality

### Phase 3: Dashboard (Week 5-6)
- Develop data visualization components
- Build overview cards and metrics
- Implement search and filter functionality
- Create export options

### Phase 4: Integration (Week 7-8)
- Connect components to Convex backend
- Implement state management patterns
- Add comprehensive testing
- Optimize performance and accessibility

## 6. Code Examples and Templates

### Component Template
```typescript
import { cva, type VariantProps } from "class-variance-authority"
import { cn } from "@/lib/utils"

const componentVariants = cva(
  "base-classes",
  {
    variants: {
      context: {
        therapeutic: "bg-therapeutic-blue-50 border-therapeutic-blue-100",
        mindful: "bg-mindful-green-50 border-mindful-green-100",
        reflective: "bg-gentle-purple-50 border-gentle-purple-100",
      }
    }
  }
)

interface ComponentProps extends VariantProps<typeof componentVariants> {
  emotionalState?: 'positive' | 'neutral' | 'reflective' | 'challenging';
  className?: string;
}

export function Component({ context, emotionalState, className }: ComponentProps) {
  return (
    <div className={cn(componentVariants({ context }), className)}>
      {/* Component content */}
    </div>
  )
}
```

This comprehensive guide provides everything needed to implement the MindMirror UI design system while maintaining consistency with the existing codebase and ensuring optimal user experience for mental health journaling.
