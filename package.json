{"name": "mindmirror", "private": true, "workspaces": ["apps/*", "packages/*"], "scripts": {"check": "biome check --write .", "dev": "bun run --filter '*' dev", "build": "bun run --filter '*' build", "check-types": "bun run --filter '*' check-types", "dev:native": "bun run --filter native dev", "dev:web": "bun run --filter web dev", "dev:server": "bun run --filter @mindmirror/backend dev", "dev:setup": "bun run --filter @mindmirror/backend dev:setup"}, "dependencies": {}, "devDependencies": {"@biomejs/biome": "2.1.2", "ultracite": "5.1.1"}, "packageManager": "bun@1.2.19"}