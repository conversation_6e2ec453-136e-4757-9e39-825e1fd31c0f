# MindMirror Design System Extensions

## Mental Health-Specific Color Palette

### Calming Colors (extending existing neutral base)
```css
:root {
  /* Therapeutic Blues - for breathing exercises and calm states */
  --therapeutic-blue-50: oklch(0.97 0.013 240);
  --therapeutic-blue-100: oklch(0.93 0.025 240);
  --therapeutic-blue-500: oklch(0.6 0.118 240);
  --therapeutic-blue-600: oklch(0.5 0.142 240);

  /* Mindful Greens - for positive energy and growth */
  --mindful-green-50: oklch(0.97 0.013 150);
  --mindful-green-100: oklch(0.93 0.025 150);
  --mindful-green-500: oklch(0.6 0.118 150);
  --mindful-green-600: oklch(0.5 0.142 150);

  /* Gentle Purples - for reflection and introspection */
  --gentle-purple-50: oklch(0.97 0.013 280);
  --gentle-purple-100: oklch(0.93 0.025 280);
  --gentle-purple-500: oklch(0.6 0.118 280);
  --gentle-purple-600: oklch(0.5 0.142 280);

  /* Warm Neutrals - for comfort and safety */
  --warm-neutral-50: oklch(0.97 0.005 60);
  --warm-neutral-100: oklch(0.93 0.01 60);
  --warm-neutral-500: oklch(0.6 0.02 60);
  --warm-neutral-600: oklch(0.5 0.025 60);
}
```

### Emotional State Colors
```css
:root {
  /* Energy States */
  --energy-high: var(--mindful-green-500);
  --energy-medium: var(--warm-neutral-500);
  --energy-low: var(--therapeutic-blue-500);
  --energy-drain: oklch(0.6 0.08 20);

  /* Mood Indicators */
  --mood-positive: var(--mindful-green-500);
  --mood-neutral: var(--warm-neutral-500);
  --mood-reflective: var(--gentle-purple-500);
  --mood-challenging: oklch(0.65 0.1 40);
}
```

## Typography Extensions

### Journal-Specific Typography
```css
.journal-text {
  font-family: var(--font-sans);
  line-height: 1.7;
  letter-spacing: 0.01em;
}

.journal-prompt {
  font-weight: 500;
  color: var(--gentle-purple-600);
  font-size: 0.95rem;
}

.journal-entry {
  font-size: 1rem;
  color: var(--foreground);
  min-height: 120px;
}

.poetic-placeholder {
  color: var(--muted-foreground);
  font-style: italic;
  opacity: 0.8;
}
```

## Animation Specifications

### Breathing Exercise Animations
```css
@keyframes breathe-in {
  0% { transform: scale(1); opacity: 0.7; }
  100% { transform: scale(1.1); opacity: 1; }
}

@keyframes breathe-out {
  0% { transform: scale(1.1); opacity: 1; }
  100% { transform: scale(1); opacity: 0.7; }
}

@keyframes gentle-pulse {
  0%, 100% { opacity: 0.8; }
  50% { opacity: 1; }
}
```

### Transition Specifications
```css
.gentle-transition {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.fade-in {
  animation: fadeIn 0.5s ease-in-out;
}

@keyframes fadeIn {
  from { opacity: 0; transform: translateY(10px); }
  to { opacity: 1; transform: translateY(0); }
}
```

## Accessibility Standards for Mental Health Applications

### Focus Management
- **High contrast focus rings** for users with visual impairments
- **Logical tab order** through journal forms
- **Skip links** for screen reader users
- **Focus trapping** in modal dialogs

### Color Accessibility
- **Minimum 4.5:1 contrast ratio** for all text
- **Color-blind friendly palette** with sufficient differentiation
- **Never rely solely on color** for conveying information
- **Alternative text** for all emotional state indicators

### Cognitive Accessibility
- **Clear, simple language** in all prompts and instructions
- **Consistent navigation patterns** to reduce cognitive load
- **Progress indicators** for multi-step forms
- **Auto-save functionality** to prevent data loss
- **Gentle error messages** that don't increase anxiety

### Screen Reader Support
- **Semantic HTML structure** with proper headings
- **ARIA labels** for interactive elements
- **Live regions** for dynamic content updates
- **Descriptive link text** and button labels

## Component Specification Template

```typescript
interface MindMirrorComponentProps {
  // Standard props
  className?: string;
  children?: React.ReactNode;
  
  // Accessibility props
  'aria-label'?: string;
  'aria-describedby'?: string;
  
  // Mental health specific props
  emotionalState?: 'positive' | 'neutral' | 'reflective' | 'challenging';
  energyLevel?: 'high' | 'medium' | 'low' | 'drain';
  
  // Interaction props
  onSave?: () => void;
  autoSave?: boolean;
  gentleValidation?: boolean;
}
```

## Responsive Design Strategy

### Breakpoints (using existing TailwindCSS)
- **Mobile First**: 320px - 768px (primary focus for journaling)
- **Tablet**: 768px - 1024px (dashboard viewing)
- **Desktop**: 1024px+ (comprehensive dashboard)

### Mobile Considerations
- **Touch-friendly targets** (minimum 44px)
- **Simplified navigation** for small screens
- **Optimized chart displays** for mobile viewing
- **Swipe gestures** for navigation between journal days
