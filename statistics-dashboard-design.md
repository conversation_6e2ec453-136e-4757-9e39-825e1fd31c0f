# Statistics Dashboard Design Specification

## Overview
The statistics dashboard provides users with comprehensive insights into their mental health journey through data visualization, trend analysis, and historical pattern recognition. The interface emphasizes meaningful insights over raw data display.

## Core Design Principles
- **Insight-Driven**: Focus on actionable patterns rather than overwhelming data
- **Emotionally Supportive**: Present data in encouraging, non-judgmental ways
- **Progressive Disclosure**: Start with overview, allow drilling down into details
- **Accessible Visualization**: Color-blind friendly charts with alternative text

## Dashboard Layout Structure

### Main Dashboard Container
```typescript
interface DashboardLayoutProps {
  timeRange: 'week' | 'month' | 'quarter' | 'year';
  selectedMetrics: string[];
  onTimeRangeChange: (range: string) => void;
  onMetricToggle: (metric: string) => void;
}

// Visual Layout:
// ┌─────────────────────────────────────────────────────────────┐
// │ 📊 Your Reflection Journey                                  │
// │ ┌─────────────┐ ┌─────────────┐ ┌─────────────┐           │
// │ │ Overview    │ │ Patterns    │ │ Insights    │           │
// │ └─────────────┘ └─────────────┘ └─────────────┘           │
// │                                                             │
// │ ┌─────────────────────────────────────────────────────────┐ │
// │ │ Time Range: [Week] [Month] [Quarter] [Year]             │ │
// │ └─────────────────────────────────────────────────────────┘ │
// │                                                             │
// │ ┌─────────────────┐ ┌─────────────────┐ ┌─────────────────┐ │
// │ │ Energy Trends   │ │ Sleep Quality   │ │ Mood Patterns   │ │
// │ │                 │ │                 │ │                 │ │
// │ └─────────────────┘ └─────────────────┘ └─────────────────┘ │
// └─────────────────────────────────────────────────────────────┘
```

## Data Visualization Components

### Energy Trend Chart
```typescript
interface EnergyTrendChartProps {
  data: EnergyDataPoint[];
  timeRange: string;
  showDrainingSources?: boolean;
  showEnergizingSources?: boolean;
}

interface EnergyDataPoint {
  date: Date;
  energyLevel: number; // 1-10 scale
  energizingThoughts: string[];
  drainingThoughts: string[];
  overallMood: 'positive' | 'neutral' | 'reflective' | 'challenging';
}

// Visual Design:
// ┌─────────────────────────────────────────────────────────────┐
// │ ⚡ Energy Patterns Over Time                                │
// │                                                             │
// │ 10 ┌─────────────────────────────────────────────────────┐ │
// │    │     ●                                               │ │
// │  8 │   ●   ●     ●                                       │ │
// │    │ ●       ● ●   ●                                     │ │
// │  6 │           ●     ●   ●                               │ │
// │    │                   ●   ●                             │ │
// │  4 │                       ●                             │ │
// │    └─────────────────────────────────────────────────────┘ │
// │    Mon  Tue  Wed  Thu  Fri  Sat  Sun                      │
// │                                                             │
// │ 💡 Insight: Your energy peaks on Wednesdays and dips on    │
// │    weekends. Consider what Wednesday activities energize    │
// │    you most.                                                │
// └─────────────────────────────────────────────────────────────┘
```

**Chart Specifications:**
- **Color Scheme**: Gradient from `var(--energy-low)` to `var(--energy-high)`
- **Interactive Points**: Hover to see specific thoughts/activities
- **Trend Lines**: Smooth curves with subtle shadows
- **Accessibility**: Screen reader compatible with data tables

### Sleep Quality Visualization
```typescript
interface SleepQualityChartProps {
  data: SleepDataPoint[];
  showCorrelations?: boolean;
  timeRange: string;
}

interface SleepDataPoint {
  date: Date;
  hoursSlept: number;
  qualityRating: number; // 1-10
  sleepTime: string;
  wakeTime: string;
  dreamNotes?: string;
  nextDayEnergy?: number;
}

// Visual Design:
// ┌─────────────────────────────────────────────────────────────┐
// │ 😴 Sleep Quality & Duration                                 │
// │                                                             │
// │ Quality ┌─────────────────────────────────────────────────┐ │
// │    10   │ ████████████████████████████████████████████████ │ │
// │     8   │ ████████████████████████████████████████████████ │ │
// │     6   │ ████████████████████████████████████████████████ │ │
// │     4   │ ████████████████████████████████████████████████ │ │
// │         └─────────────────────────────────────────────────┘ │
// │ Hours   ┌─────────────────────────────────────────────────┐ │
// │    10   │ ████████████████████████████████████████████████ │ │
// │     8   │ ████████████████████████████████████████████████ │ │
// │     6   │ ████████████████████████████████████████████████ │ │
// │         └─────────────────────────────────────────────────┘ │
// │         Mon  Tue  Wed  Thu  Fri  Sat  Sun                  │
// │                                                             │
// │ 🔗 Correlation: Better sleep quality correlates with       │
// │    higher next-day energy levels (r=0.73)                  │
// └─────────────────────────────────────────────────────────────┘
```

### Mood Pattern Heatmap
```typescript
interface MoodPatternHeatmapProps {
  data: MoodDataPoint[];
  viewType: 'daily' | 'weekly' | 'monthly';
  showLabels?: boolean;
}

// Visual Design:
// ┌─────────────────────────────────────────────────────────────┐
// │ 🎭 Mood Patterns by Day & Time                             │
// │                                                             │
// │        Mon  Tue  Wed  Thu  Fri  Sat  Sun                   │
// │ Week 1 🟢   🟡   🟢   🟢   🟡   🔵   🔵                    │
// │ Week 2 🟡   🟢   🟢   🟡   🟠   🔵   🟢                    │
// │ Week 3 🟢   🟢   🟡   🟢   🟢   🟡   🔵                    │
// │ Week 4 🟡   🟢   🟢   🟢   🟠   🟡   🟡                    │
// │                                                             │
// │ 🟢 Positive  🟡 Neutral  🔵 Reflective  🟠 Challenging     │
// │                                                             │
// │ 📈 Pattern: Fridays tend to be more challenging.           │
// │    Consider planning lighter activities on Fridays.        │
// └─────────────────────────────────────────────────────────────┘
```

## Historical Data Navigation Interface

### Time Range Selector
```typescript
interface TimeRangeSelectorProps {
  currentRange: TimeRange;
  availableRanges: TimeRange[];
  onRangeChange: (range: TimeRange) => void;
  customRangeEnabled?: boolean;
}

// Visual Design:
// ┌─────────────────────────────────────────────────────────────┐
// │ 📅 Time Period                                              │
// │ ┌─────┐ ┌─────┐ ┌─────┐ ┌─────┐ ┌─────────────┐           │
// │ │Week │ │Month│ │ 3M  │ │Year │ │Custom Range │           │
// │ └─────┘ └─────┘ └─────┘ └─────┘ └─────────────┘           │
// │                                                             │
// │ ← Previous Period    Current Period    Next Period →       │
// └─────────────────────────────────────────────────────────────┘
```

### Calendar Navigation
```typescript
interface CalendarNavigationProps {
  selectedDate: Date;
  availableDates: Date[];
  onDateSelect: (date: Date) => void;
  showCompletionStatus?: boolean;
}

// Visual Design:
// ┌─────────────────────────────────────────────────────────────┐
// │ 📆 January 2024                                            │
// │                                                             │
// │ Sun Mon Tue Wed Thu Fri Sat                                │
// │  ●   ●   ●   ●   ●   ○   ○                                 │
// │  1   2   3   4   5   6   7                                 │
// │                                                             │
// │  ●   ●   ○   ●   ●   ●   ○                                 │
// │  8   9  10  11  12  13  14                                 │
// │                                                             │
// │ ● Completed  ○ Incomplete  ◐ Partial                       │
// └─────────────────────────────────────────────────────────────┘
```

## Summary Statistics and Key Insights

### Overview Cards
```typescript
interface OverviewCardProps {
  title: string;
  value: string | number;
  trend?: 'up' | 'down' | 'stable';
  trendValue?: string;
  insight?: string;
  icon?: string;
}

// Visual Design:
// ┌─────────────────┐ ┌─────────────────┐ ┌─────────────────┐
// │ 📊 Total Entries│ │ ⚡ Avg Energy   │ │ 🔥 Current Streak│
// │                 │ │                 │ │                 │
// │      47         │ │      7.2        │ │      12 days    │
// │   ↗️ +3 this week│ │   ↗️ +0.8 vs last│ │   🎯 Personal best│
// │                 │ │      week       │ │                 │
// └─────────────────┘ └─────────────────┘ └─────────────────┘
```

### Insights Panel
```typescript
interface InsightsPanelProps {
  insights: Insight[];
  showPersonalized?: boolean;
  allowDismiss?: boolean;
}

interface Insight {
  type: 'pattern' | 'correlation' | 'achievement' | 'suggestion';
  title: string;
  description: string;
  confidence: number; // 0-1
  actionable?: string;
}

// Visual Design:
// ┌─────────────────────────────────────────────────────────────┐
// │ 💡 Personalized Insights                                   │
// │                                                             │
// │ 🎯 Pattern Detected                                        │
// │ Your energy levels are 23% higher on days when you        │
// │ journal in the morning vs evening.                         │
// │ → Try setting a morning journaling reminder                │
// │                                                             │
// │ 🌱 Growth Milestone                                        │
// │ You've maintained consistent journaling for 2 weeks!       │
// │ This is linked to improved sleep quality.                  │
// │                                                             │
// │ 🔍 Correlation Found                                       │
// │ Days with outdoor activities show 31% better mood ratings. │
// │ → Consider scheduling more outdoor time                     │
// └─────────────────────────────────────────────────────────────┘
```

## Search and Filter Interface

### Advanced Search Component
```typescript
interface AdvancedSearchProps {
  onSearch: (criteria: SearchCriteria) => void;
  savedSearches?: SavedSearch[];
  recentSearches?: string[];
}

interface SearchCriteria {
  textQuery?: string;
  dateRange?: [Date, Date];
  moodFilters?: string[];
  energyRange?: [number, number];
  sleepQualityRange?: [number, number];
  tags?: string[];
}

// Visual Design:
// ┌─────────────────────────────────────────────────────────────┐
// │ 🔍 Search Your Journey                                      │
// │                                                             │
// │ ┌─────────────────────────────────────────────────────────┐ │
// │ │ Search thoughts, feelings, activities...                │ │
// │ └─────────────────────────────────────────────────────────┘ │
// │                                                             │
// │ Filters:                                                    │
// │ 📅 Date Range: [Last 30 days ▼]                           │
// │ 😊 Mood: [All] [Positive] [Neutral] [Reflective] [Challenging] │
// │ ⚡ Energy: [1] ────●──── [10]                              │
// │ 😴 Sleep: [1] ──●────── [10]                               │
// │                                                             │
// │ [Clear Filters] [Save Search] [Search]                     │
// └─────────────────────────────────────────────────────────────┘
```

### Filter Results Display
```typescript
interface FilterResultsProps {
  results: JournalEntry[];
  totalCount: number;
  currentPage: number;
  onPageChange: (page: number) => void;
  sortBy: 'date' | 'relevance' | 'energy' | 'mood';
  onSortChange: (sort: string) => void;
}

// Visual Design:
// ┌─────────────────────────────────────────────────────────────┐
// │ 📋 Search Results (23 entries found)                       │
// │                                                             │
// │ Sort by: [Date ▼] [Relevance] [Energy] [Mood]              │
// │                                                             │
// │ ┌─────────────────────────────────────────────────────────┐ │
// │ │ Jan 15, 2024 • Energy: 8/10 • Mood: Positive           │ │
// │ │ "Today I felt energized when I completed my morning..." │ │
// │ │ Tags: morning-routine, exercise, positive               │ │
// │ └─────────────────────────────────────────────────────────┘ │
// │                                                             │
// │ [Previous] Page 1 of 3 [Next]                              │
// └─────────────────────────────────────────────────────────────┘
```

## Export and Sharing Functionality

### Export Options Component
```typescript
interface ExportOptionsProps {
  selectedDateRange: [Date, Date];
  selectedMetrics: string[];
  onExport: (format: ExportFormat, options: ExportOptions) => void;
}

interface ExportOptions {
  format: 'pdf' | 'csv' | 'json';
  includeCharts: boolean;
  includeInsights: boolean;
  includeRawData: boolean;
  anonymize: boolean;
}

// Visual Design:
// ┌─────────────────────────────────────────────────────────────┐
// │ 📤 Export Your Data                                        │
// │                                                             │
// │ Date Range: Jan 1 - Jan 31, 2024 (31 entries)             │
// │                                                             │
// │ Format:                                                     │
// │ ○ PDF Report (charts + insights)                           │
// │ ○ CSV Data (spreadsheet compatible)                        │
// │ ○ JSON (raw data for developers)                           │
// │                                                             │
// │ Include:                                                    │
// │ ☑️ Charts and visualizations                                │
// │ ☑️ AI-generated insights                                    │
// │ ☑️ Raw journal entries                                      │
// │ ☑️ Anonymize sensitive data                                 │
// │                                                             │
// │ [Preview] [Export] [Share with Therapist]                  │
// └─────────────────────────────────────────────────────────────┘
```

## Component Implementation Examples

### Dashboard Card Component
```typescript
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { TrendingUp, TrendingDown, Minus } from "lucide-react"
import { cn } from "@/lib/utils"

interface DashboardCardProps {
  title: string;
  value: string | number;
  trend?: 'up' | 'down' | 'stable';
  trendValue?: string;
  insight?: string;
  icon?: React.ReactNode;
  className?: string;
}

export function DashboardCard({
  title,
  value,
  trend,
  trendValue,
  insight,
  icon,
  className
}: DashboardCardProps) {
  const TrendIcon = trend === 'up' ? TrendingUp : trend === 'down' ? TrendingDown : Minus;

  return (
    <Card className={cn("gentle-transition hover:shadow-md", className)}>
      <CardHeader className="pb-2">
        <CardTitle className="flex items-center gap-2 text-sm font-medium text-muted-foreground">
          {icon}
          {title}
        </CardTitle>
      </CardHeader>
      <CardContent>
        <div className="text-2xl font-bold">{value}</div>
        {trend && trendValue && (
          <div className={cn(
            "flex items-center gap-1 text-xs mt-1",
            trend === 'up' ? "text-mindful-green-600" :
            trend === 'down' ? "text-energy-drain" :
            "text-muted-foreground"
          )}>
            <TrendIcon className="w-3 h-3" />
            {trendValue}
          </div>
        )}
        {insight && (
          <p className="text-xs text-muted-foreground mt-2">{insight}</p>
        )}
      </CardContent>
    </Card>
  )
}
```

### Chart Container Component
```typescript
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { MoreHorizontal, Download, Maximize2 } from "lucide-react"

interface ChartContainerProps {
  title: string;
  children: React.ReactNode;
  onExport?: () => void;
  onExpand?: () => void;
  insight?: string;
  className?: string;
}

export function ChartContainer({
  title,
  children,
  onExport,
  onExpand,
  insight,
  className
}: ChartContainerProps) {
  return (
    <Card className={cn("", className)}>
      <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
        <CardTitle className="text-base font-medium">{title}</CardTitle>
        <div className="flex items-center gap-1">
          {onExport && (
            <Button variant="ghost" size="icon" onClick={onExport}>
              <Download className="w-4 h-4" />
            </Button>
          )}
          {onExpand && (
            <Button variant="ghost" size="icon" onClick={onExpand}>
              <Maximize2 className="w-4 h-4" />
            </Button>
          )}
          <Button variant="ghost" size="icon">
            <MoreHorizontal className="w-4 h-4" />
          </Button>
        </div>
      </CardHeader>
      <CardContent>
        <div className="h-[300px] w-full">
          {children}
        </div>
        {insight && (
          <div className="mt-4 p-3 bg-therapeutic-blue-50 rounded-lg border border-therapeutic-blue-100">
            <p className="text-sm text-therapeutic-blue-700">
              💡 <strong>Insight:</strong> {insight}
            </p>
          </div>
        )}
      </CardContent>
    </Card>
  )
}
```
