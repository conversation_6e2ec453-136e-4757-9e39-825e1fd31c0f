# MindMirror Development Rules and Guidelines

## Overview
This document establishes development standards and guidelines for the MindMirror project based on the existing codebase architecture and mental health application requirements.

## Code Organization and Architecture

### File Structure Standards
```
apps/web/src/
├── components/
│   ├── ui/                    # shadcn/ui base components
│   ├── journal/               # Journal-specific components
│   ├── dashboard/             # Dashboard components
│   ├── shared/                # Shared mental health components
│   └── layout/                # Layout components
├── hooks/                     # Custom React hooks
├── lib/                       # Utility functions
├── routes/                    # TanStack Router pages
└── styles/                    # Global styles and themes
```

### Component Development Rules

#### 1. Component Naming and Structure
- **PascalCase** for component names and files
- **kebab-case** for CSS classes and data attributes
- **camelCase** for props and variables
- Use descriptive names that reflect mental health context

```typescript
// ✅ Good
export function JournalEntryCard({ emotionalState, energyLevel }: JournalEntryCardProps) {}

// ❌ Bad  
export function Card({ state, level }: CardProps) {}
```

#### 2. TypeScript Standards
- **Strict typing** for all components and functions
- **Interface definitions** for all props
- **Generic types** where appropriate for reusability
- **Mental health specific types** for domain concepts

```typescript
// ✅ Good
interface JournalEntryProps {
  emotionalState: 'positive' | 'neutral' | 'reflective' | 'challenging';
  energyLevel: 1 | 2 | 3 | 4 | 5 | 6 | 7 | 8 | 9 | 10;
  onSave: (entry: JournalEntry) => Promise<void>;
  autoSave?: boolean;
}

// ❌ Bad
interface Props {
  state: string;
  level: number;
  onSave: any;
}
```

#### 3. Component Composition Patterns
- **Compound components** for complex UI elements
- **Render props** for flexible behavior
- **Forward refs** for proper DOM access
- **Consistent data-slot attributes** following shadcn/ui patterns

```typescript
// ✅ Good - Compound component pattern
export function JournalForm({ children }: JournalFormProps) {
  return <form className="journal-form">{children}</form>
}

JournalForm.Step = function JournalFormStep({ children }: StepProps) {
  return <div className="journal-form-step">{children}</div>
}

JournalForm.Input = function JournalFormInput(props: InputProps) {
  return <JournalInput {...props} />
}
```

## Styling and Design Standards

### 1. TailwindCSS Usage Rules
- **Utility-first approach** with semantic class combinations
- **Custom CSS only** when Tailwind utilities are insufficient
- **Responsive design** with mobile-first approach
- **Mental health color palette** integration

```typescript
// ✅ Good
<div className="bg-therapeutic-blue-50 border border-therapeutic-blue-100 rounded-xl p-6 gentle-transition">

// ❌ Bad
<div className="bg-blue-50 border border-blue-100 rounded-xl p-6" style={{ transition: 'all 0.3s' }}>
```

### 2. CSS Custom Properties
- **Use semantic color names** from the mental health palette
- **Consistent spacing** using design tokens
- **Animation variables** for consistent timing

```css
/* ✅ Good */
.energy-indicator {
  background-color: var(--energy-medium);
  transition: var(--gentle-transition);
}

/* ❌ Bad */
.energy-indicator {
  background-color: #3b82f6;
  transition: all 0.3s ease;
}
```

### 3. Component Variants with CVA
- **Class Variance Authority** for all component variants
- **Semantic variant names** reflecting mental health context
- **Consistent default variants** across components

```typescript
const journalCardVariants = cva(
  "rounded-xl border p-6 gentle-transition",
  {
    variants: {
      context: {
        therapeutic: "bg-therapeutic-blue-50 border-therapeutic-blue-100",
        mindful: "bg-mindful-green-50 border-mindful-green-100",
        reflective: "bg-gentle-purple-50 border-gentle-purple-100",
        warm: "bg-warm-neutral-50 border-warm-neutral-100",
      },
      size: {
        sm: "p-4 text-sm",
        md: "p-6 text-base",
        lg: "p-8 text-lg",
      }
    },
    defaultVariants: {
      context: "therapeutic",
      size: "md",
    }
  }
)
```

## State Management Guidelines

### 1. TanStack Query Integration
- **Convex queries** for all server state
- **Optimistic updates** for better UX
- **Error boundaries** for graceful error handling
- **Cache invalidation** strategies for data consistency

```typescript
// ✅ Good
export function useJournalEntry(entryId: string) {
  return useSuspenseQuery(
    convexQuery(api.journal.getEntry, { id: entryId }),
    {
      staleTime: 5 * 60 * 1000, // 5 minutes
      gcTime: 10 * 60 * 1000,   // 10 minutes
    }
  )
}

// ❌ Bad
export function useJournalEntry(entryId: string) {
  const [entry, setEntry] = useState(null)
  
  useEffect(() => {
    fetch(`/api/journal/${entryId}`)
      .then(res => res.json())
      .then(setEntry)
  }, [entryId])
  
  return entry
}
```

### 2. Form State Management
- **TanStack Form** for complex forms
- **Zod validation** for type-safe form validation
- **Auto-save functionality** for journal entries
- **Gentle validation** with encouraging messages

```typescript
// ✅ Good
const journalEntrySchema = z.object({
  energizingThoughts: z.string()
    .min(1, "Please share what energized you today")
    .max(1000, "Take your time, but try to keep it under 1000 characters"),
  drainingThoughts: z.string()
    .min(1, "It's okay to acknowledge what drained you"),
})

// ❌ Bad
const journalEntrySchema = z.object({
  energizingThoughts: z.string().min(1, "Required field"),
  drainingThoughts: z.string().min(1, "Required field"),
})
```

## Accessibility Requirements

### 1. Semantic HTML
- **Proper heading hierarchy** (h1 → h2 → h3)
- **Landmark elements** (main, nav, aside, section)
- **Form labels** and descriptions
- **Button vs link** semantic distinction

```typescript
// ✅ Good
<main>
  <h1>Your Reflection Journey</h1>
  <section aria-labelledby="today-section">
    <h2 id="today-section">Today's Entry</h2>
    <form>
      <label htmlFor="thoughts">What energized you today?</label>
      <textarea 
        id="thoughts"
        aria-describedby="thoughts-help"
        required
      />
      <div id="thoughts-help">Share your positive experiences</div>
    </form>
  </section>
</main>

// ❌ Bad
<div>
  <div className="text-2xl font-bold">Your Reflection Journey</div>
  <div>
    <div className="text-xl">Today's Entry</div>
    <div>
      <div>What energized you today?</div>
      <textarea placeholder="Type here..." />
    </div>
  </div>
</div>
```

### 2. ARIA Attributes
- **aria-label** for buttons without text
- **aria-describedby** for additional context
- **aria-live** for dynamic content updates
- **role** attributes when semantic HTML isn't sufficient

### 3. Keyboard Navigation
- **Tab order** follows logical flow
- **Focus indicators** are clearly visible
- **Escape key** closes modals/dropdowns
- **Enter/Space** activates buttons

### 4. Color and Contrast
- **4.5:1 minimum** contrast ratio for normal text
- **3:1 minimum** for large text and UI elements
- **Never rely solely on color** for conveying information
- **Color-blind friendly** palette choices

## Performance Guidelines

### 1. Component Optimization
- **React.memo** for expensive components
- **useMemo/useCallback** for expensive calculations
- **Lazy loading** for route components
- **Code splitting** for large features

```typescript
// ✅ Good
const ExpensiveChart = React.memo(function ExpensiveChart({ data }: ChartProps) {
  const processedData = useMemo(() => 
    processChartData(data), 
    [data]
  )
  
  return <Chart data={processedData} />
})

// ❌ Bad
function ExpensiveChart({ data }: ChartProps) {
  const processedData = processChartData(data) // Runs on every render
  return <Chart data={processedData} />
}
```

### 2. Bundle Size Management
- **Tree shaking** for unused code elimination
- **Dynamic imports** for non-critical features
- **Bundle analysis** to identify large dependencies
- **Selective imports** from large libraries

```typescript
// ✅ Good
import { format } from 'date-fns/format'
import { parseISO } from 'date-fns/parseISO'

// ❌ Bad
import * as dateFns from 'date-fns'
```

### 3. Data Loading Strategies
- **Suspense boundaries** for loading states
- **Prefetching** for anticipated navigation
- **Pagination** for large datasets
- **Virtualization** for long lists

## Testing Standards

### 1. Unit Testing
- **Vitest** for test runner
- **Testing Library** for component testing
- **MSW** for API mocking
- **Coverage targets**: 80% minimum for critical paths

### 2. Integration Testing
- **End-to-end flows** for journal entry process
- **Convex integration** testing
- **Error boundary** testing
- **Accessibility** testing with jest-axe

### 3. Visual Testing
- **Storybook** for component documentation
- **Chromatic** for visual regression testing
- **Responsive design** testing across breakpoints
- **Dark mode** compatibility testing

## Security and Privacy

### 1. Data Handling
- **Input sanitization** for all user content
- **XSS prevention** through proper escaping
- **CSRF protection** for state-changing operations
- **Sensitive data** encryption at rest

### 2. Mental Health Considerations
- **Data minimization** - collect only necessary information
- **User consent** for data processing
- **Export functionality** for data portability
- **Deletion capabilities** for user control

### 3. Error Handling
- **Graceful degradation** for network failures
- **User-friendly error messages** without technical details
- **Error reporting** without exposing sensitive data
- **Retry mechanisms** for transient failures

## Code Review Guidelines

### 1. Review Checklist
- [ ] TypeScript types are properly defined
- [ ] Accessibility requirements are met
- [ ] Mental health context is appropriate
- [ ] Performance considerations are addressed
- [ ] Tests cover critical functionality
- [ ] Documentation is updated

### 2. Mental Health Specific Reviews
- [ ] Language is supportive and non-judgmental
- [ ] Color choices support emotional safety
- [ ] User privacy is protected
- [ ] Error messages are encouraging
- [ ] Loading states don't increase anxiety

### 3. Code Quality Standards
- [ ] No console.log statements in production
- [ ] Proper error boundaries are in place
- [ ] Components are properly memoized
- [ ] CSS classes follow naming conventions
- [ ] Git commits are descriptive and atomic
